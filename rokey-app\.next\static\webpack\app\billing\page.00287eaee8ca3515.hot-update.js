"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/billing/page",{

/***/ "(app-pages-browser)/./src/app/billing/page.tsx":
/*!**********************************!*\
  !*** ./src/app/billing/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BillingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* harmony import */ var _components_DebugSubscriptionStatus__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/DebugSubscriptionStatus */ \"(app-pages-browser)/./src/components/DebugSubscriptionStatus.tsx\");\n/* harmony import */ var _components_DebugWebhookLogs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/DebugWebhookLogs */ \"(app-pages-browser)/./src/components/DebugWebhookLogs.tsx\");\n/* harmony import */ var canvas_confetti__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! canvas-confetti */ \"(app-pages-browser)/./node_modules/canvas-confetti/dist/confetti.module.mjs\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* harmony import */ var _emailjs_browser__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @emailjs/browser */ \"(app-pages-browser)/./node_modules/@emailjs/browser/es/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst plans = [\n    {\n        id: 'free',\n        name: 'Free',\n        price: 0,\n        interval: 'forever',\n        features: [\n            {\n                name: 'Strict fallback routing only',\n                included: true\n            },\n            {\n                name: 'Basic analytics',\n                included: true\n            },\n            {\n                name: 'No custom roles',\n                included: false\n            },\n            {\n                name: 'Configurations',\n                included: true,\n                limit: '1 max'\n            },\n            {\n                name: 'API keys per config',\n                included: true,\n                limit: '3 max'\n            },\n            {\n                name: 'User-generated API keys',\n                included: true,\n                limit: '3 max'\n            }\n        ]\n    },\n    {\n        id: 'starter',\n        name: 'Starter',\n        price: 19,\n        interval: 'month',\n        popular: true,\n        features: [\n            {\n                name: 'All routing strategies',\n                included: true\n            },\n            {\n                name: 'Advanced analytics',\n                included: true\n            },\n            {\n                name: 'Custom roles',\n                included: true,\n                limit: 'Up to 3 roles'\n            },\n            {\n                name: 'Configurations',\n                included: true,\n                limit: '5 max'\n            },\n            {\n                name: 'API keys per config',\n                included: true,\n                limit: '15 max'\n            },\n            {\n                name: 'User-generated API keys',\n                included: true,\n                limit: '50 max'\n            },\n            {\n                name: 'Browsing tasks',\n                included: true,\n                limit: '15/month'\n            }\n        ]\n    },\n    {\n        id: 'professional',\n        name: 'Professional',\n        price: 49,\n        interval: 'month',\n        features: [\n            {\n                name: 'Everything in Starter',\n                included: true\n            },\n            {\n                name: 'Unlimited configurations',\n                included: true\n            },\n            {\n                name: 'Unlimited API keys per config',\n                included: true\n            },\n            {\n                name: 'Unlimited user-generated API keys',\n                included: true\n            },\n            {\n                name: 'Knowledge base documents',\n                included: true,\n                limit: '5 documents'\n            },\n            {\n                name: 'Priority support',\n                included: true\n            }\n        ]\n    },\n    {\n        id: 'enterprise',\n        name: 'Enterprise',\n        price: 149,\n        interval: 'month',\n        features: [\n            {\n                name: 'Everything in Professional',\n                included: true\n            },\n            {\n                name: 'Unlimited knowledge base documents',\n                included: true\n            },\n            {\n                name: 'Advanced semantic caching',\n                included: true\n            },\n            {\n                name: 'Custom integrations',\n                included: true\n            },\n            {\n                name: 'Dedicated support + phone',\n                included: true\n            },\n            {\n                name: 'SLA guarantee',\n                included: true\n            }\n        ]\n    }\n];\nfunction BillingPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { user, subscriptionStatus, refreshStatus: refreshSubscription, createCheckoutSession, openCustomerPortal } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_5__.useSubscription)();\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_10__.useConfirmation)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCancelModal, setShowCancelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cancelReason, setCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [cancelFeedback, setCancelFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tierBeforePortal, setTierBeforePortal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const currentPlan = plans.find((plan)=>plan.id === (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier)) || plans[0];\n    // Calculate days until renewal based on actual subscription data\n    const daysUntilRenewal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"BillingPage.useMemo[daysUntilRenewal]\": ()=>{\n            if ((subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' || !(subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.currentPeriodEnd)) {\n                return null;\n            }\n            const renewalDate = new Date(subscriptionStatus.currentPeriodEnd);\n            const today = new Date();\n            const diffTime = renewalDate.getTime() - today.getTime();\n            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n            // Return null if subscription has already expired\n            return diffDays > 0 ? diffDays : null;\n        }\n    }[\"BillingPage.useMemo[daysUntilRenewal]\"], [\n        subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier,\n        subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.currentPeriodEnd\n    ]);\n    // Helper function to determine if a tier change is an upgrade\n    const isUpgrade = (fromTier, toTier)=>{\n        const tierOrder = {\n            'free': 0,\n            'starter': 1,\n            'professional': 2,\n            'enterprise': 3\n        };\n        return tierOrder[toTier] > tierOrder[fromTier];\n    };\n    // Handle return from Customer Portal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BillingPage.useEffect\": ()=>{\n            const portalReturn = searchParams.get('portal_return');\n            const previousTier = searchParams.get('prev_tier');\n            if (portalReturn === 'true') {\n                console.log('Returned from Customer Portal, previous tier:', previousTier);\n                // Remove the parameters from URL\n                const newUrl = new URL(window.location.href);\n                newUrl.searchParams.delete('portal_return');\n                newUrl.searchParams.delete('prev_tier');\n                window.history.replaceState({}, '', newUrl.toString());\n                // Wait for webhook to process, then refresh subscription status\n                const refreshWithRetry = {\n                    \"BillingPage.useEffect.refreshWithRetry\": async ()=>{\n                        let attempts = 0;\n                        const maxAttempts = 8; // Increased attempts\n                        const initialDelay = 3000; // Longer initial delay for webhook processing\n                        const retryDelay = 2000; // 2 seconds between retry attempts\n                        console.log('Starting subscription refresh with retry logic...');\n                        // Initial delay to allow webhook processing\n                        await new Promise({\n                            \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, initialDelay)\n                        }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                        while(attempts < maxAttempts){\n                            try {\n                                console.log(\"Refresh attempt \".concat(attempts + 1, \"/\").concat(maxAttempts));\n                                // Force a complete refresh\n                                await refreshSubscription();\n                                // Wait a moment for the state to update\n                                await new Promise({\n                                    \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, 500)\n                                }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                                // Get fresh subscription status after refresh\n                                const response = await fetch(\"/api/stripe/subscription-status?userId=\".concat(user === null || user === void 0 ? void 0 : user.id, \"&_t=\").concat(Date.now()), {\n                                    cache: 'no-store',\n                                    headers: {\n                                        'Cache-Control': 'no-cache, no-store, must-revalidate',\n                                        'Pragma': 'no-cache',\n                                        'Expires': '0'\n                                    }\n                                });\n                                if (response.ok) {\n                                    const freshStatus = await response.json();\n                                    const currentTier = freshStatus.tier || 'free';\n                                    console.log('Fresh tier from API:', currentTier, 'Previous tier:', previousTier);\n                                    // Check if tier actually changed\n                                    if (currentTier !== previousTier) {\n                                        const wasUpgrade = isUpgrade(previousTier || 'free', currentTier);\n                                        if (wasUpgrade) {\n                                            // Show upgrade success message with confetti\n                                            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('Plan upgraded successfully!');\n                                            // Trigger confetti for upgrades only\n                                            const triggerConfetti = {\n                                                \"BillingPage.useEffect.refreshWithRetry.triggerConfetti\": ()=>{\n                                                    (0,canvas_confetti__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({\n                                                        particleCount: 100,\n                                                        spread: 70,\n                                                        origin: {\n                                                            y: 0.6\n                                                        }\n                                                    });\n                                                }\n                                            }[\"BillingPage.useEffect.refreshWithRetry.triggerConfetti\"];\n                                            triggerConfetti();\n                                            setTimeout(triggerConfetti, 500);\n                                            setTimeout(triggerConfetti, 1000);\n                                        } else {\n                                            // Show generic success message for downgrades/cancellations\n                                            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('Billing settings updated successfully!');\n                                        }\n                                        console.log('Plan change detected and processed successfully');\n                                        break;\n                                    } else if (attempts >= 3) {\n                                        // After a few attempts, show success message even if no change detected\n                                        // (user might have just viewed the portal without making changes)\n                                        sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('Billing settings updated successfully!');\n                                        console.log('No plan change detected, but showing success message');\n                                        break;\n                                    }\n                                }\n                                attempts++;\n                                if (attempts < maxAttempts) {\n                                    console.log(\"No change detected yet, waiting \".concat(retryDelay, \"ms before next attempt...\"));\n                                    await new Promise({\n                                        \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, retryDelay)\n                                    }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                                }\n                            } catch (error) {\n                                console.error(\"Refresh attempt \".concat(attempts + 1, \" failed:\"), error);\n                                attempts++;\n                                if (attempts >= maxAttempts) {\n                                    console.log('Max refresh attempts reached, forcing page reload...');\n                                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Refreshing page to update subscription status...');\n                                    // Force a page reload as last resort\n                                    setTimeout({\n                                        \"BillingPage.useEffect.refreshWithRetry\": ()=>{\n                                            window.location.reload();\n                                        }\n                                    }[\"BillingPage.useEffect.refreshWithRetry\"], 2000);\n                                } else {\n                                    await new Promise({\n                                        \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, retryDelay)\n                                    }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                                }\n                            }\n                        }\n                    }\n                }[\"BillingPage.useEffect.refreshWithRetry\"];\n                refreshWithRetry();\n            }\n        }\n    }[\"BillingPage.useEffect\"], [\n        searchParams,\n        refreshSubscription,\n        user === null || user === void 0 ? void 0 : user.id\n    ]); // Removed subscriptionStatus?.tier from dependencies\n    const handleChangePlan = async ()=>{\n        // For existing users (anyone who can access the billing page), always use Customer Portal\n        // The billing page is only accessible to authenticated users with accounts\n        try {\n            setLoading(true);\n            // Create return URL with current domain, portal return parameter, and current tier\n            const currentTier = (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) || 'free';\n            const returnUrl = \"\".concat(window.location.origin, \"/billing?portal_return=true&prev_tier=\").concat(currentTier);\n            await openCustomerPortal(returnUrl);\n        } catch (error) {\n            console.error('Customer portal error:', error);\n            // If portal is not configured, fall back to showing a helpful message\n            if (error.message.includes('No configuration provided') || error.message.includes('default configuration has not been created')) {\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Billing portal is being set up. Please contact support for plan changes.');\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Failed to open billing portal. Please try again.');\n            }\n            setLoading(false);\n        }\n    };\n    const handleCancelSubscription = async ()=>{\n        if (!cancelReason.trim()) {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Please select a reason for cancellation');\n            return;\n        }\n        setLoading(true);\n        try {\n            var _user_user_metadata;\n            // Send cancellation feedback via EmailJS\n            const templateParams = {\n                user_email: (user === null || user === void 0 ? void 0 : user.email) || 'Unknown',\n                user_name: (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.first_name) || 'User',\n                current_plan: currentPlan.name,\n                cancel_reason: cancelReason,\n                additional_feedback: cancelFeedback,\n                cancel_date: new Date().toLocaleDateString()\n            };\n            await _emailjs_browser__WEBPACK_IMPORTED_MODULE_11__[\"default\"].send(\"service_2xtn7iv\", \"template_pg7e1af\", templateParams, \"lm7-ATth2Cql60KIN\");\n            // Here you would also cancel the subscription in your payment processor\n            // For now, we'll simulate the process\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('Subscription cancelled successfully. We\\'ve sent your feedback to our team.');\n            setShowCancelModal(false);\n            setCancelReason('');\n            setCancelFeedback('');\n            await refreshSubscription();\n        } catch (error) {\n            console.error('Cancellation error:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Failed to cancel subscription. Please contact support.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const cancelReasons = [\n        'Too expensive',\n        'Not using enough features',\n        'Found a better alternative',\n        'Technical issues',\n        'Poor customer support',\n        'Missing features I need',\n        'Temporary financial constraints',\n        'Other'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-800/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-semibold text-white\",\n                                        children: \"Billing & Plans\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-3 py-1 text-sm bg-cyan-500 text-white rounded\",\n                                            children: \"Subscription\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: refreshSubscription,\n                                    className: \"px-3 py-1 bg-gray-800 border border-gray-700 rounded text-sm text-white hover:bg-gray-700 hover:border-gray-600 focus:outline-none focus:border-cyan-500\",\n                                    size: \"sm\",\n                                    children: \"\\uD83D\\uDD04 Refresh Status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-4 sm:px-6 lg:px-8 py-8 space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6 hover:shadow-lg transition-all duration-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-cyan-500/10 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5 text-cyan-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-white\",\n                                                children: \"Current Plan\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 13\n                                    }, this),\n                                    (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) !== 'free' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>setShowCancelModal(true),\n                                        className: \"text-red-400 border-red-500/20 hover:bg-red-500/10 hover:border-red-500/40 transition-all duration-200 text-sm\",\n                                        size: \"sm\",\n                                        children: \"Cancel Subscription\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-4 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: currentPlan.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    currentPlan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 text-xs bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full font-semibold\",\n                                                        children: \"⭐ Popular\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: currentPlan.price === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400\",\n                                                            children: \"Free\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"$\",\n                                                                currentPlan.price,\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400 text-lg font-normal\",\n                                                                    children: [\n                                                                        \"/\",\n                                                                        currentPlan.interval\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 47\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    currentPlan.price === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-green-400 font-medium\",\n                                                        children: \"Forever free\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col justify-center\",\n                                        children: daysUntilRenewal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-800/50 rounded-lg p-4 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center gap-2 text-sm text-gray-300 mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Next Billing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white font-semibold\",\n                                                    children: [\n                                                        daysUntilRenewal,\n                                                        \" days\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-500/10 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white\",\n                                                children: \"Plan Features\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: currentPlan.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3 py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: feature.included ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-1 bg-green-500/10 rounded-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4 text-green-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-1 bg-gray-700/50 rounded-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm \".concat(feature.included ? 'text-gray-300' : 'text-gray-500'),\n                                                        children: [\n                                                            feature.name,\n                                                            feature.limit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400 font-medium\",\n                                                                children: [\n                                                                    \" (\",\n                                                                    feature.limit,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-orange-500/10 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5 text-orange-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white\",\n                                                children: \"Manage Subscription\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-4 bg-gray-800/30 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"inline-flex items-center gap-2 bg-green-500/10 text-green-400 px-3 py-1 rounded-full text-sm font-semibold mb-3 border border-green-500/20\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Active Plan\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-xl font-bold text-white mb-1\",\n                                                        children: currentPlan.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: currentPlan.price === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400\",\n                                                            children: \"Free\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"$\",\n                                                                currentPlan.price,\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400 text-base font-normal\",\n                                                                    children: [\n                                                                        \"/\",\n                                                                        currentPlan.interval\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 47\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: handleChangePlan,\n                                                disabled: loading,\n                                                className: \"w-full bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white shadow-lg hover:shadow-xl transition-all duration-200\",\n                                                size: \"lg\",\n                                                children: (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 497,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Upgrade Plan\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Manage Subscription\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-center text-sm text-gray-400\",\n                                                children: (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? 'Unlock advanced features and higher limits' : 'Change plans, update billing, or cancel anytime'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-green-500/10 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white\",\n                                                children: \"Billing Details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center py-2 border-b border-gray-700/30\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Plan\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-white\",\n                                                        children: currentPlan.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center py-2 border-b border-gray-700/30\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Billing Cycle\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-white\",\n                                                        children: currentPlan.price === 0 ? 'N/A' : \"Monthly\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 15\n                                            }, this),\n                                            daysUntilRenewal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center py-2 border-b border-gray-700/30\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Next Billing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-white\",\n                                                        children: new Date(Date.now() + daysUntilRenewal * 24 * 60 * 60 * 1000).toLocaleDateString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 text-xs rounded-full font-medium \".concat((subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? 'bg-gray-700/50 text-gray-300' : 'bg-green-500/10 text-green-400'),\n                                                        children: (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? 'Free Plan' : 'Active'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-purple-500/10 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-purple-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white\",\n                                                children: \"Support\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 text-sm\",\n                                                children: \"Need help with your subscription or billing? We're here to assist you.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        className: \"w-full justify-start text-gray-300 border-gray-600 hover:bg-gray-800/50 hover:border-gray-500 text-sm\",\n                                                        onClick: ()=>window.open('/contact', '_blank'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 580,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Contact Support\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 text-center\",\n                                                        children: \"Response time: Usually within 24 hours\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 562,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 9\n                    }, this),\n                    showCancelModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900 border border-gray-800 rounded-xl shadow-2xl max-w-md w-full p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-6 w-6 text-red-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white\",\n                                            children: \"Cancel Subscription\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-4\",\n                                    children: \"We're sorry to see you go! Please help us improve by telling us why you're cancelling.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-white mb-2\",\n                                                    children: \"Reason for cancellation *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: cancelReason,\n                                                    onChange: (e)=>setCancelReason(e.target.value),\n                                                    className: \"w-full p-2 border border-gray-600 rounded-lg bg-gray-800 text-white focus:ring-2 focus:ring-orange-500 focus:border-orange-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Select a reason...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 614,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        cancelReasons.map((reason)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: reason,\n                                                                children: reason\n                                                            }, reason, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-white mb-2\",\n                                                    children: \"Additional feedback (optional)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: cancelFeedback,\n                                                    onChange: (e)=>setCancelFeedback(e.target.value),\n                                                    placeholder: \"Tell us more about your experience or what we could do better...\",\n                                                    rows: 3,\n                                                    className: \"w-full p-2 border border-gray-600 rounded-lg bg-gray-800 text-white placeholder-gray-400 focus:ring-2 focus:ring-orange-500 focus:border-orange-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowCancelModal(false),\n                                            className: \"flex-1 text-gray-300 border-gray-600 hover:bg-gray-800/50\",\n                                            children: \"Keep Subscription\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: handleCancelSubscription,\n                                            disabled: loading || !cancelReason.trim(),\n                                            className: \"flex-1 bg-red-600 hover:bg-red-700 text-white\",\n                                            children: loading ? 'Cancelling...' : 'Cancel Subscription'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 635,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 11\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DebugSubscriptionStatus__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 658,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DebugWebhookLogs__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 659,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 657,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        isOpen: confirmation.isOpen,\n                        onClose: confirmation.hideConfirmation,\n                        onConfirm: confirmation.onConfirm,\n                        title: confirmation.title,\n                        message: confirmation.message,\n                        confirmText: confirmation.confirmText,\n                        cancelText: confirmation.cancelText,\n                        type: confirmation.type,\n                        isLoading: confirmation.isLoading\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 664,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n        lineNumber: 331,\n        columnNumber: 5\n    }, this);\n}\n_s(BillingPage, \"ao9wCuBkrny2/ktyGsoTqY438I0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_5__.useSubscription,\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_10__.useConfirmation\n    ];\n});\n_c = BillingPage;\nvar _c;\n$RefreshReg$(_c, \"BillingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/billing/page.tsx\n"));

/***/ })

});