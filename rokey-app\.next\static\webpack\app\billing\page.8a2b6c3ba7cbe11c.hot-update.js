"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/billing/page",{

/***/ "(app-pages-browser)/./src/app/billing/page.tsx":
/*!**********************************!*\
  !*** ./src/app/billing/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BillingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* harmony import */ var canvas_confetti__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! canvas-confetti */ \"(app-pages-browser)/./node_modules/canvas-confetti/dist/confetti.module.mjs\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* harmony import */ var _emailjs_browser__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @emailjs/browser */ \"(app-pages-browser)/./node_modules/@emailjs/browser/es/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst plans = [\n    {\n        id: 'free',\n        name: 'Free',\n        price: 0,\n        interval: 'forever',\n        features: [\n            {\n                name: 'Strict fallback routing only',\n                included: true\n            },\n            {\n                name: 'Basic analytics',\n                included: true\n            },\n            {\n                name: 'No custom roles',\n                included: false\n            },\n            {\n                name: 'Configurations',\n                included: true,\n                limit: '1 max'\n            },\n            {\n                name: 'API keys per config',\n                included: true,\n                limit: '3 max'\n            },\n            {\n                name: 'User-generated API keys',\n                included: true,\n                limit: '3 max'\n            }\n        ]\n    },\n    {\n        id: 'starter',\n        name: 'Starter',\n        price: 19,\n        interval: 'month',\n        popular: true,\n        features: [\n            {\n                name: 'All routing strategies',\n                included: true\n            },\n            {\n                name: 'Advanced analytics',\n                included: true\n            },\n            {\n                name: 'Custom roles',\n                included: true,\n                limit: 'Up to 3 roles'\n            },\n            {\n                name: 'Configurations',\n                included: true,\n                limit: '5 max'\n            },\n            {\n                name: 'API keys per config',\n                included: true,\n                limit: '15 max'\n            },\n            {\n                name: 'User-generated API keys',\n                included: true,\n                limit: '50 max'\n            },\n            {\n                name: 'Browsing tasks',\n                included: true,\n                limit: '15/month'\n            }\n        ]\n    },\n    {\n        id: 'professional',\n        name: 'Professional',\n        price: 49,\n        interval: 'month',\n        features: [\n            {\n                name: 'Everything in Starter',\n                included: true\n            },\n            {\n                name: 'Unlimited configurations',\n                included: true\n            },\n            {\n                name: 'Unlimited API keys per config',\n                included: true\n            },\n            {\n                name: 'Unlimited user-generated API keys',\n                included: true\n            },\n            {\n                name: 'Knowledge base documents',\n                included: true,\n                limit: '5 documents'\n            },\n            {\n                name: 'Priority support',\n                included: true\n            }\n        ]\n    },\n    {\n        id: 'enterprise',\n        name: 'Enterprise',\n        price: 149,\n        interval: 'month',\n        features: [\n            {\n                name: 'Everything in Professional',\n                included: true\n            },\n            {\n                name: 'Unlimited knowledge base documents',\n                included: true\n            },\n            {\n                name: 'Advanced semantic caching',\n                included: true\n            },\n            {\n                name: 'Custom integrations',\n                included: true\n            },\n            {\n                name: 'Dedicated support + phone',\n                included: true\n            },\n            {\n                name: 'SLA guarantee',\n                included: true\n            }\n        ]\n    }\n];\nfunction BillingPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { user, subscriptionStatus, refreshStatus: refreshSubscription, createCheckoutSession, openCustomerPortal } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_5__.useSubscription)();\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_8__.useConfirmation)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCancelModal, setShowCancelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cancelReason, setCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [cancelFeedback, setCancelFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tierBeforePortal, setTierBeforePortal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const currentPlan = plans.find((plan)=>plan.id === (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier)) || plans[0];\n    // Calculate days until renewal based on actual subscription data\n    const daysUntilRenewal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"BillingPage.useMemo[daysUntilRenewal]\": ()=>{\n            if ((subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' || !(subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.currentPeriodEnd)) {\n                return null;\n            }\n            const renewalDate = new Date(subscriptionStatus.currentPeriodEnd);\n            const today = new Date();\n            const diffTime = renewalDate.getTime() - today.getTime();\n            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n            // Return null if subscription has already expired\n            return diffDays > 0 ? diffDays : null;\n        }\n    }[\"BillingPage.useMemo[daysUntilRenewal]\"], [\n        subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier,\n        subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.currentPeriodEnd\n    ]);\n    // Helper function to determine if a tier change is an upgrade\n    const isUpgrade = (fromTier, toTier)=>{\n        const tierOrder = {\n            'free': 0,\n            'starter': 1,\n            'professional': 2,\n            'enterprise': 3\n        };\n        return tierOrder[toTier] > tierOrder[fromTier];\n    };\n    // Handle return from Customer Portal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BillingPage.useEffect\": ()=>{\n            const portalReturn = searchParams.get('portal_return');\n            const previousTier = searchParams.get('prev_tier');\n            if (portalReturn === 'true') {\n                console.log('Returned from Customer Portal, previous tier:', previousTier);\n                // Remove the parameters from URL\n                const newUrl = new URL(window.location.href);\n                newUrl.searchParams.delete('portal_return');\n                newUrl.searchParams.delete('prev_tier');\n                window.history.replaceState({}, '', newUrl.toString());\n                // Wait for webhook to process, then refresh subscription status\n                const refreshWithRetry = {\n                    \"BillingPage.useEffect.refreshWithRetry\": async ()=>{\n                        let attempts = 0;\n                        const maxAttempts = 8; // Increased attempts\n                        const initialDelay = 3000; // Longer initial delay for webhook processing\n                        const retryDelay = 2000; // 2 seconds between retry attempts\n                        console.log('Starting subscription refresh with retry logic...');\n                        // Initial delay to allow webhook processing\n                        await new Promise({\n                            \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, initialDelay)\n                        }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                        while(attempts < maxAttempts){\n                            try {\n                                console.log(\"Refresh attempt \".concat(attempts + 1, \"/\").concat(maxAttempts));\n                                // Force a complete refresh\n                                await refreshSubscription();\n                                // Wait a moment for the state to update\n                                await new Promise({\n                                    \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, 500)\n                                }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                                // Get fresh subscription status after refresh\n                                const response = await fetch(\"/api/stripe/subscription-status?userId=\".concat(user === null || user === void 0 ? void 0 : user.id, \"&_t=\").concat(Date.now()), {\n                                    cache: 'no-store',\n                                    headers: {\n                                        'Cache-Control': 'no-cache, no-store, must-revalidate',\n                                        'Pragma': 'no-cache',\n                                        'Expires': '0'\n                                    }\n                                });\n                                if (response.ok) {\n                                    const freshStatus = await response.json();\n                                    const currentTier = freshStatus.tier || 'free';\n                                    console.log('Fresh tier from API:', currentTier, 'Previous tier:', previousTier);\n                                    // Check if tier actually changed\n                                    if (currentTier !== previousTier) {\n                                        const wasUpgrade = isUpgrade(previousTier || 'free', currentTier);\n                                        if (wasUpgrade) {\n                                            // Show upgrade success message with confetti\n                                            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('Plan upgraded successfully!');\n                                            // Trigger confetti for upgrades only\n                                            const triggerConfetti = {\n                                                \"BillingPage.useEffect.refreshWithRetry.triggerConfetti\": ()=>{\n                                                    (0,canvas_confetti__WEBPACK_IMPORTED_MODULE_7__[\"default\"])({\n                                                        particleCount: 100,\n                                                        spread: 70,\n                                                        origin: {\n                                                            y: 0.6\n                                                        }\n                                                    });\n                                                }\n                                            }[\"BillingPage.useEffect.refreshWithRetry.triggerConfetti\"];\n                                            triggerConfetti();\n                                            setTimeout(triggerConfetti, 500);\n                                            setTimeout(triggerConfetti, 1000);\n                                        } else {\n                                            // Show generic success message for downgrades/cancellations\n                                            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('Billing settings updated successfully!');\n                                        }\n                                        console.log('Plan change detected and processed successfully');\n                                        break;\n                                    } else if (attempts >= 3) {\n                                        // After a few attempts, show success message even if no change detected\n                                        // (user might have just viewed the portal without making changes)\n                                        sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('Billing settings updated successfully!');\n                                        console.log('No plan change detected, but showing success message');\n                                        break;\n                                    }\n                                }\n                                attempts++;\n                                if (attempts < maxAttempts) {\n                                    console.log(\"No change detected yet, waiting \".concat(retryDelay, \"ms before next attempt...\"));\n                                    await new Promise({\n                                        \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, retryDelay)\n                                    }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                                }\n                            } catch (error) {\n                                console.error(\"Refresh attempt \".concat(attempts + 1, \" failed:\"), error);\n                                attempts++;\n                                if (attempts >= maxAttempts) {\n                                    console.log('Max refresh attempts reached, forcing page reload...');\n                                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Refreshing page to update subscription status...');\n                                    // Force a page reload as last resort\n                                    setTimeout({\n                                        \"BillingPage.useEffect.refreshWithRetry\": ()=>{\n                                            window.location.reload();\n                                        }\n                                    }[\"BillingPage.useEffect.refreshWithRetry\"], 2000);\n                                } else {\n                                    await new Promise({\n                                        \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, retryDelay)\n                                    }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                                }\n                            }\n                        }\n                    }\n                }[\"BillingPage.useEffect.refreshWithRetry\"];\n                refreshWithRetry();\n            }\n        }\n    }[\"BillingPage.useEffect\"], [\n        searchParams,\n        refreshSubscription,\n        user === null || user === void 0 ? void 0 : user.id\n    ]); // Removed subscriptionStatus?.tier from dependencies\n    const handleChangePlan = async ()=>{\n        // For existing users (anyone who can access the billing page), always use Customer Portal\n        // The billing page is only accessible to authenticated users with accounts\n        try {\n            setLoading(true);\n            // Create return URL with current domain, portal return parameter, and current tier\n            const currentTier = (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) || 'free';\n            const returnUrl = \"\".concat(window.location.origin, \"/billing?portal_return=true&prev_tier=\").concat(currentTier);\n            await openCustomerPortal(returnUrl);\n        } catch (error) {\n            console.error('Customer portal error:', error);\n            // If portal is not configured, fall back to showing a helpful message\n            if (error.message.includes('No configuration provided') || error.message.includes('default configuration has not been created')) {\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Billing portal is being set up. Please contact support for plan changes.');\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Failed to open billing portal. Please try again.');\n            }\n            setLoading(false);\n        }\n    };\n    const handleCancelSubscription = async ()=>{\n        if (!cancelReason.trim()) {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Please select a reason for cancellation');\n            return;\n        }\n        setLoading(true);\n        try {\n            var _user_user_metadata;\n            // Send cancellation feedback via EmailJS\n            const templateParams = {\n                user_email: (user === null || user === void 0 ? void 0 : user.email) || 'Unknown',\n                user_name: (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.first_name) || 'User',\n                current_plan: currentPlan.name,\n                cancel_reason: cancelReason,\n                additional_feedback: cancelFeedback,\n                cancel_date: new Date().toLocaleDateString()\n            };\n            await _emailjs_browser__WEBPACK_IMPORTED_MODULE_9__[\"default\"].send(\"service_2xtn7iv\", \"template_pg7e1af\", templateParams, \"lm7-ATth2Cql60KIN\");\n            // Here you would also cancel the subscription in your payment processor\n            // For now, we'll simulate the process\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('Subscription cancelled successfully. We\\'ve sent your feedback to our team.');\n            setShowCancelModal(false);\n            setCancelReason('');\n            setCancelFeedback('');\n            await refreshSubscription();\n        } catch (error) {\n            console.error('Cancellation error:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Failed to cancel subscription. Please contact support.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const cancelReasons = [\n        'Too expensive',\n        'Not using enough features',\n        'Found a better alternative',\n        'Technical issues',\n        'Poor customer support',\n        'Missing features I need',\n        'Temporary financial constraints',\n        'Other'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-800/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-semibold text-white\",\n                                        children: \"Billing & Plans\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-3 py-1 text-sm bg-cyan-500 text-white rounded\",\n                                            children: \"Subscription\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-4 sm:px-6 lg:px-8 py-8 space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6 hover:shadow-lg transition-all duration-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-cyan-500/10 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 text-cyan-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-white\",\n                                                children: \"Current Plan\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 13\n                                    }, this),\n                                    (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) !== 'free' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>setShowCancelModal(true),\n                                        className: \"text-red-400 border-red-500/20 hover:bg-red-500/10 hover:border-red-500/40 transition-all duration-200 text-sm\",\n                                        size: \"sm\",\n                                        children: \"Cancel Subscription\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-4 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: currentPlan.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    currentPlan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 text-xs bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full font-semibold\",\n                                                        children: \"⭐ Popular\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: currentPlan.price === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400\",\n                                                            children: \"Free\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"$\",\n                                                                currentPlan.price,\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400 text-lg font-normal\",\n                                                                    children: [\n                                                                        \"/\",\n                                                                        currentPlan.interval\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 47\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    currentPlan.price === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-green-400 font-medium\",\n                                                        children: \"Forever free\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col justify-center\",\n                                        children: daysUntilRenewal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-800/50 rounded-lg p-4 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center gap-2 text-sm text-gray-300 mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Next Billing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white font-semibold\",\n                                                    children: [\n                                                        daysUntilRenewal,\n                                                        \" days\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-500/10 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white\",\n                                                children: \"Plan Features\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: currentPlan.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3 py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: feature.included ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-1 bg-green-500/10 rounded-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4 text-green-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-1 bg-gray-700/50 rounded-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm \".concat(feature.included ? 'text-gray-300' : 'text-gray-500'),\n                                                        children: [\n                                                            feature.name,\n                                                            feature.limit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400 font-medium\",\n                                                                children: [\n                                                                    \" (\",\n                                                                    feature.limit,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-orange-500/10 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 text-orange-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white\",\n                                                children: \"Manage Subscription\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-4 bg-gray-800/30 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"inline-flex items-center gap-2 bg-green-500/10 text-green-400 px-3 py-1 rounded-full text-sm font-semibold mb-3 border border-green-500/20\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Active Plan\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-xl font-bold text-white mb-1\",\n                                                        children: currentPlan.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: currentPlan.price === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400\",\n                                                            children: \"Free\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 476,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"$\",\n                                                                currentPlan.price,\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400 text-base font-normal\",\n                                                                    children: [\n                                                                        \"/\",\n                                                                        currentPlan.interval\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                    lineNumber: 478,\n                                                                    columnNumber: 47\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: handleChangePlan,\n                                                disabled: loading,\n                                                className: \"w-full bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white shadow-lg hover:shadow-xl transition-all duration-200\",\n                                                size: \"lg\",\n                                                children: (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Upgrade Plan\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 497,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Manage Subscription\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-center text-sm text-gray-400\",\n                                                children: (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? 'Unlock advanced features and higher limits' : 'Change plans, update billing, or cancel anytime'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-green-500/10 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white\",\n                                                children: \"Billing Details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center py-2 border-b border-gray-700/30\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Plan\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-white\",\n                                                        children: currentPlan.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center py-2 border-b border-gray-700/30\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Billing Cycle\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-white\",\n                                                        children: currentPlan.price === 0 ? 'N/A' : \"Monthly\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 15\n                                            }, this),\n                                            daysUntilRenewal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center py-2 border-b border-gray-700/30\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Next Billing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-white\",\n                                                        children: new Date(Date.now() + daysUntilRenewal * 24 * 60 * 60 * 1000).toLocaleDateString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 text-xs rounded-full font-medium \".concat((subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? 'bg-gray-700/50 text-gray-300' : 'bg-green-500/10 text-green-400'),\n                                                        children: (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? 'Free Plan' : 'Active'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 516,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-purple-500/10 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-5 w-5 text-purple-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white\",\n                                                children: \"Support\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 558,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 text-sm\",\n                                                children: \"Need help with your subscription or billing? We're here to assist you.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 566,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        className: \"w-full justify-start text-gray-300 border-gray-600 hover:bg-gray-800/50 hover:border-gray-500 text-sm\",\n                                                        onClick: ()=>window.open('/contact', '_blank'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Contact Support\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 text-center\",\n                                                        children: \"Response time: Usually within 24 hours\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 578,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 569,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 557,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 514,\n                        columnNumber: 9\n                    }, this),\n                    showCancelModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-xl shadow-2xl max-w-md w-full p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 bg-red-500/10 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-5 w-5 text-red-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 592,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 591,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white\",\n                                            children: \"Cancel Subscription\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-6 text-sm\",\n                                    children: \"We're sorry to see you go! Please help us improve by telling us why you're cancelling.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 597,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-white mb-2\",\n                                                    children: \"Reason for cancellation *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: cancelReason,\n                                                    onChange: (e)=>setCancelReason(e.target.value),\n                                                    className: \"w-full p-3 border border-gray-600 rounded-lg bg-gray-800 text-white focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Select a reason...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        cancelReasons.map((reason)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: reason,\n                                                                children: reason\n                                                            }, reason, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 613,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-white mb-2\",\n                                                    children: \"Additional feedback (optional)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: cancelFeedback,\n                                                    onChange: (e)=>setCancelFeedback(e.target.value),\n                                                    placeholder: \"Tell us more about your experience or what we could do better...\",\n                                                    rows: 3,\n                                                    className: \"w-full p-3 border border-gray-600 rounded-lg bg-gray-800 text-white placeholder-gray-400 focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 text-sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 618,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowCancelModal(false),\n                                            className: \"flex-1 text-gray-300 border-gray-600 hover:bg-gray-800/50 text-sm\",\n                                            children: \"Keep Subscription\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: handleCancelSubscription,\n                                            disabled: loading || !cancelReason.trim(),\n                                            className: \"flex-1 bg-red-600 hover:bg-red-700 text-white text-sm\",\n                                            children: loading ? 'Cancelling...' : 'Cancel Subscription'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 589,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 588,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        isOpen: confirmation.isOpen,\n                        onClose: confirmation.hideConfirmation,\n                        onConfirm: confirmation.onConfirm,\n                        title: confirmation.title,\n                        message: confirmation.message,\n                        confirmText: confirmation.confirmText,\n                        cancelText: confirmation.cancelText,\n                        type: confirmation.type,\n                        isLoading: confirmation.isLoading\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 655,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 352,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n        lineNumber: 331,\n        columnNumber: 5\n    }, this);\n}\n_s(BillingPage, \"ao9wCuBkrny2/ktyGsoTqY438I0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_5__.useSubscription,\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_8__.useConfirmation\n    ];\n});\n_c = BillingPage;\nvar _c;\n$RefreshReg$(_c, \"BillingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/billing/page.tsx\n"));

/***/ })

});